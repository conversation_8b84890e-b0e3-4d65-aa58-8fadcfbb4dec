export const programTypes = [
  { id: 'backend', name: 'Back End Development', icon: '/icons/server.svg' },
  { id: 'frontend', name: 'Front End Development', icon: '/icons/laptop.svg' },
  { id: 'mobile', name: 'Mobile App Development', icon: '/icons/phone.svg' }
];

export const mentors = [
  {
    id: 1,
    name: '<PERSON>',
    role: 'Nest JS Program',
    image: '/theresa.png',
    bio: 'Experienced Nest.js developer with 8+ years in backend development. Specializes in building scalable microservices and RESTful APIs with TypeScript and Node.js.',
    socialLinks: { twitter: '#', linkedin: '#' }
  },
  {
    id: 2,
    name: '<PERSON>',
    role: 'Golang Program',
    image: '/courtney.png',
    bio: 'Golang expert with experience at major tech companies. Passionate about teaching concurrent programming and building high-performance systems with Go.',
    socialLinks: { twitter: '#', linkedin: '#' }
  },
  {
    id: 3,
    name: '<PERSON>',
    role: 'Next JS',
    image: '/albert.png',
    bio: 'Next.js specialist with 6+ years of frontend development experience. Expert in server-side rendering, static site generation, and React ecosystem.',
    socialLinks: { twitter: '#', linkedin: '#' }
  },
  {
    id: 4,
    name: '<PERSON> Mc<PERSON>inney',
    role: 'React JS',
    image: '/marvin.png',
    bio: 'React.js developer with 7+ years of experience building modern web applications. Specializes in state management, hooks, and performance optimization.',
    socialLinks: { twitter: '#', linkedin: '#' }
  },
  {
    id: 5,
    name: 'Jessica Parker',
    role: 'Vue JS',
    image: '/theresa.png',
    bio: 'Vue.js expert with 5+ years of experience. Passionate about component-based architecture, Vuex state management, and building responsive UIs.',
    socialLinks: { twitter: '#', linkedin: '#' }
  },
  {
    id: 6,
    name: 'Daniel Wilson',
    role: 'Angular',
    image: '/albert.png',
    bio: 'Angular developer with 6+ years of experience building enterprise applications. Expert in TypeScript, RxJS, and NgRx for state management.',
    socialLinks: { twitter: '#', linkedin: '#' }
  },
  {
    id: 7,
    name: 'Sophia Martinez',
    role: 'Flutter',
    image: '/courtney.png',
    bio: 'Flutter developer with expertise in cross-platform mobile app development. Specializes in creating beautiful, responsive UIs with Dart programming language.',
    socialLinks: { twitter: '#', linkedin: '#' }
  }
];

export const programs = [
  {
    id: 1,
    type: 'frontend',
    title: 'Next Js',
    description: 'Build modern React applications with server-side rendering, static site generation, and more.',
    mentors: [
      {
        id: 1,
        name: 'Patricia Vero',
        image: '/theresa.png',
        price: 700000,
        duration: '5 Weeks',
        role: 'Senior Next.js Developer',
        company: 'Vercel',
        bio: 'Patricia is a seasoned Next.js developer with extensive experience building scalable web applications. She specializes in server-side rendering and static site generation techniques.'
      },
      {
        id: 2,
        name: 'Albert Flores',
        image: '/albert.png',
        price: 650000,
        duration: '4 Weeks',
        role: 'Lead Front-End Engineer',
        company: 'Meta',
        bio: 'Albert has worked on numerous high-traffic websites using Next.js and React. He focuses on performance optimization and modern front-end architecture.'
      },
      {
        id: 3,
        name: 'Theresa Webb',
        image: '/theresa.png',
        price: 750000,
        duration: '6 Weeks',
        role: 'Full-Stack Developer',
        company: 'Vercel',
        bio: 'Theresa brings a comprehensive approach to Next.js development, with expertise in both front-end and back-end integration. Her courses cover the entire development lifecycle.'
      }
    ]
  },
  {
    id: 2,
    type: 'frontend',
    title: 'React Js',
    description: 'Master React and build interactive user interfaces with the most popular front-end library.',
    mentors: [
      {
        id: 1,
        name: 'Marvin McKinney',
        image: '/marvin.png',
        price: 600000,
        duration: '4 Weeks',
        role: 'Senior React Developer',
        company: 'Meta',
        bio: 'Marvin has been working with React since its early days and has contributed to several open-source React libraries. He specializes in state management and component architecture.'
      },
      {
        id: 2,
        name: 'Courtney Henry',
        image: '/courtney.png',
        price: 550000,
        duration: '3 Weeks',
        role: 'UI/UX Engineer',
        company: 'Airbnb',
        bio: 'Courtney combines design expertise with React development skills to create beautiful, functional user interfaces. Her approach focuses on user experience and accessibility.'
      }
    ]
  },
  {
    id: 3,
    type: 'frontend',
    title: 'Vue Js',
    description: 'Learn to build dynamic single-page applications with Vue.js, a progressive JavaScript framework.',
    mentors: [
      {
        id: 1,
        name: 'Patricia Vero',
        image: '/theresa.png',
        price: 700000,
        duration: '5 Weeks',
        role: 'Senior Vue.js Developer',
        company: 'Vercel',
        bio: 'Patricia has extensive experience with Vue.js ecosystem and has built numerous production applications. She specializes in state management with Vuex and single-page application architecture.'
      },
      {
        id: 2,
        name: 'John Smith',
        image: '/albert.png',
        price: 650000,
        duration: '4 Weeks',
        role: 'Front-End Architect',
        company: 'Netlify',
        bio: 'John focuses on building high-performance Vue.js applications with an emphasis on progressive web app capabilities and optimization techniques.'
      }
    ]
  },
  {
    id: 4,
    type: 'backend',
    title: 'Golang',
    description: "Master Google's programming language to build fast, reliable, and efficient back-end systems.",
    mentors: [
      {
        id: 1,
        name: 'Cindy Cloe',
        image: '/courtney.png',
        price: 500000,
        duration: '3 Weeks',
        role: 'Senior Go Developer',
        company: 'Google',
        bio: 'Cindy specializes in building high-performance microservices with Go. Her expertise includes concurrency patterns and efficient system design.'
      },
      {
        id: 2,
        name: 'Michael Brown',
        image: '/marvin.png',
        price: 550000,
        duration: '4 Weeks',
        role: 'Backend Systems Engineer',
        company: 'Cloudflare',
        bio: 'Michael has extensive experience building large-scale distributed systems with Go. He focuses on performance optimization and scalable architecture.'
      }
    ]
  },
  {
    id: 5,
    type: 'backend',
    title: 'Node.js',
    description: 'Learn server-side JavaScript programming with Node.js to build scalable network applications.',
    mentors: [
      {
        id: 1,
        name: 'Cindy Cloe',
        image: '/courtney.png',
        price: 500000,
        duration: '3 Weeks',
        role: 'Senior Node.js Developer',
        company: 'Microsoft',
        bio: 'Cindy has built numerous production-grade Node.js applications and APIs. She specializes in Express.js and RESTful API design.'
      },
      {
        id: 2,
        name: 'David Wilson',
        image: '/albert.png',
        price: 550000,
        duration: '4 Weeks',
        role: 'Full-Stack JavaScript Developer',
        company: 'Stripe',
        bio: 'David specializes in full-stack JavaScript development with a focus on Node.js backends. He has extensive experience with MongoDB and real-time application architecture.'
      }
    ]
  },
  {
    id: 6,
    type: 'backend',
    title: 'Nest Js',
    description: 'Build scalable and maintainable server-side applications using the power of TypeScript and Node.js.',
    mentors: [
      {
        id: 1,
        name: 'Cindy Cloe',
        image: '/courtney.png',
        price: 500000,
        duration: '3 Weeks',
        role: 'Senior Nest.js Developer',
        company: 'Google',
        bio: 'Cindy specializes in building enterprise-grade applications with Nest.js. Her expertise includes TypeScript, dependency injection, and microservice architecture.'
      },
      {
        id: 2,
        name: 'Robert Johnson',
        image: '/marvin.png',
        price: 600000,
        duration: '5 Weeks',
        role: 'Backend Architect',
        company: 'Microsoft',
        bio: 'Robert has extensive experience designing and implementing large-scale backend systems with Nest.js. He focuses on scalable architecture and best practices.'
      }
    ]
  },
  {
    id: 7,
    type: 'frontend',
    title: 'Angular',
    description: 'Master Angular to build dynamic, single-page web applications with TypeScript.',
    mentors: [
      {
        id: 1,
        name: 'Patricia Vero',
        image: '/theresa.png',
        price: 700000,
        duration: '5 Weeks',
        role: 'Senior Angular Developer',
        company: 'Google',
        bio: 'Patricia has been working with Angular since its early versions and has built numerous enterprise applications. She specializes in reactive programming with RxJS and TypeScript.'
      },
      {
        id: 2,
        name: 'Sarah Miller',
        image: '/courtney.png',
        price: 650000,
        duration: '4 Weeks',
        role: 'Front-End Architect',
        company: 'Microsoft',
        bio: 'Sarah specializes in building large-scale Angular applications with a focus on state management using NgRx and enterprise architecture patterns.'
      }
    ]
  },
  {
    id: 8,
    type: 'frontend',
    title: 'Tailwind CSS',
    description: 'Learn to rapidly build modern websites without ever leaving your HTML with this utility-first CSS framework.',
    mentors: [
      {
        id: 1,
        name: 'Patricia Vero',
        image: '/theresa.png',
        price: 700000,
        duration: '5 Weeks',
        role: 'Senior UI Developer',
        company: 'Tailwind Labs',
        bio: 'Patricia is an expert in building modern user interfaces with Tailwind CSS. She specializes in responsive design and creating reusable component systems.'
      },
      {
        id: 2,
        name: 'James Taylor',
        image: '/albert.png',
        price: 600000,
        duration: '3 Weeks',
        role: 'UI/UX Designer & Developer',
        company: 'Figma',
        bio: 'James combines design expertise with development skills to create beautiful interfaces with Tailwind CSS. He focuses on design systems and component libraries.'
      }
    ]
  },
  {
    id: 9,
    type: 'frontend',
    title: 'TypeScript',
    description: 'Add static typing to JavaScript to improve developer productivity and code quality.',
    mentors: [
      {
        id: 1,
        name: 'Patricia Vero',
        image: '/theresa.png',
        price: 700000,
        duration: '5 Weeks',
        role: 'Senior TypeScript Developer',
        company: 'Microsoft',
        bio: 'Patricia has extensive experience with TypeScript in large-scale applications. She specializes in advanced type systems and integration with modern JavaScript frameworks.'
      },
      {
        id: 2,
        name: 'Emily Davis',
        image: '/courtney.png',
        price: 750000,
        duration: '6 Weeks',
        role: 'Software Architect',
        company: 'GitHub',
        bio: 'Emily focuses on enterprise-grade TypeScript applications with an emphasis on architecture, design patterns, and code quality. Her courses cover advanced TypeScript concepts.'
      }
    ]
  },
  {
    id: 10,
    type: 'mobile',
    title: 'Flutter',
    description: 'Build beautiful native apps for iOS and Android from a single codebase with Flutter.',
    mentors: [
      {
        id: 1,
        name: 'Sophia Martinez',
        image: '/courtney.png',
        price: 650000,
        duration: '5 Weeks',
        role: 'Senior Flutter Developer',
        company: 'Google',
        bio: 'Sophia specializes in creating beautiful, responsive mobile applications with Flutter. Her expertise includes Dart programming and cross-platform development.'
      },
      {
        id: 2,
        name: 'Daniel Lee',
        image: '/marvin.png',
        price: 700000,
        duration: '6 Weeks',
        role: 'Mobile App Architect',
        company: 'Uber',
        bio: 'Daniel has extensive experience building production Flutter applications. He focuses on state management, performance optimization, and native feature integration.'
      }
    ]
  }
];

export const testimonials = [
  {
    id: 1,
    name: 'Sarah Kristen',
    role: 'Student, National University',
    image: '/sarah.png',
    text: 'The Back-End Development Bootcamp was a game-changer for me! The mentors were incredibly supportive, and I learned Nest JS and Golang from scratch.',
    rating: 5,
    program: 'Back-End Development'
  },
  {
    id: 2,
    name: 'John Cooper',
    role: 'Student, Tech Institute',
    image: '/johncooper.png',
    text: 'Front-End bootcamp helped me transition from zero coding knowledge to a confident React developer. Amazing experience!',
    rating: 5,
    program: 'Front-End Development'
  },
  {
    id: 3,
    name: 'Maria Garcia',
    role: 'Student, Digital Academy',
    image: '/maria.png',
    text: 'The Mobile Development program exceeded my expectations. Now I can build professional Flutter applications with confidence.',
    rating: 5,
    program: 'Mobile Development'
  }
];

export const freeClasses = [
  {
    id: 1,
    title: 'Intro To Next JS: Build Modern Web Apps',
    date: '18/02/2025',
    time: '12pm - 14pm',
    image: '/class1.png',
    type: 'frontend',
    mentorImage: '/theresa.png'
  },
  {
    id: 2,
    title: 'REST API With Golang: Create Scalable',
    date: '18/02/2025',
    time: '12pm - 14pm',
    image: '/class2.png',
    type: 'backend',
    mentorImage: '/marvin.png'
  },
  {
    id: 3,
    title: 'Mobile App Development With Flutter',
    date: '18/02/2025',
    time: '12pm - 14pm',
    image: '/class3.png',
    type: 'mobile',
    mentorImage: '/courtney.png'
  },
  {
    id: 4,
    title: 'Advanced Next JS Techniques',
    date: '18/02/2025',
    time: '12pm - 14pm',
    image: '/class4.png',
    type: 'frontend',
    mentorImage: '/albert.png'
  },
  {
    id: 5,
    title: 'Building RESTful APIs with Go',
    date: '18/02/2025',
    time: '12pm - 14pm',
    image: '/class5.png',
    type: 'backend',
    mentorImage: '/theresa.png'
  },
  {
    id: 6,
    title: 'Flutter UI Design Fundamentals',
    date: '18/02/2025',
    time: '12pm - 14pm',
    image: '/class6.png',
    type: 'mobile',
    mentorImage: '/marvin.png'
  },
  {
    id: 7,
    title: 'Optimizing Next JS Performance',
    date: '18/02/2025',
    time: '12pm - 14pm',
    image: '/class7.png',
    type: 'frontend',
    mentorImage: '/courtney.png'
  },
  {
    id: 8,
    title: 'Go Microservices Workshop',
    date: '18/02/2025',
    time: '12pm - 14pm',
    image: '/class8.png',
    type: 'backend',
    mentorImage: '/albert.png'
  },
  {
    id: 9,
    title: 'State Management in Flutter',
    date: '18/02/2025',
    time: '12pm - 14pm',
    image: '/class9.png',
    type: 'mobile',
    mentorImage: '/theresa.png'
  },
  {
    id: 10,
    title: 'Server-Side Rendering with Next JS',
    date: '18/02/2025',
    time: '12pm - 14pm',
    image: '/class10.png',
    type: 'frontend',
    mentorImage: '/marvin.png'
  },
  {
    id: 11,
    title: 'Testing Go Applications',
    date: '18/02/2025',
    time: '12pm - 14pm',
    image: '/class11.png',
    type: 'backend',
    mentorImage: '/courtney.png'
  },
  {
    id: 12,
    title: 'Building Native Mobile Apps with Flutter',
    date: '18/02/2025',
    time: '12pm - 14pm',
    image: '/class12.png',
    type: 'mobile',
    mentorImage: '/albert.png'
  }
];

export const getProgramById = (id) => {
  return programs.find(program => program.id === parseInt(id));
};
